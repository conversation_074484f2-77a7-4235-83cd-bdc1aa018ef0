# Event Images Setup Guide

## Overview

The Events & Activities page now includes a comprehensive image gallery that displays event photos organized by year and category. This guide explains how to set up and manage the event images.

## Current Implementation

### Features Added

1. **Tabbed Interface**: The Events page now has two tabs:
   - **Event Cards**: The original event cards view
   - **Image Gallery**: New comprehensive image gallery

2. **Image Gallery Components**:
   - `EventImageGallery.tsx`: Main gallery component with expandable event categories
   - `ImageModal.tsx`: Modal for viewing individual images with download functionality
   - `eventImageData.ts`: Data structure containing all event images

3. **Year Filtering**: Both tabs support year filtering (All, 2023, 2022, etc.)

4. **Interactive Features**:
   - Expandable event categories
   - Image modal with zoom and download
   - Responsive grid layout
   - Smooth animations

## File Structure

```
src/
├── components/events/
│   ├── EventImageGallery.tsx    # Main gallery component
│   ├── ImageModal.tsx           # Image modal component
│   └── ...
├── data/
│   └── eventImageData.ts        # Event image data structure
├── utils/
│   ├── eventImageScanner.ts     # Utility functions for organizing images
│   └── imageImports.ts          # Image path utilities
└── pages/
    └── Events.tsx               # Updated Events page with tabs
```

## Adding Real Images

### Step 1: Prepare Images

1. **Organize Images**: Place your event images in the following structure:
   ```
   public/images/event_images/
   ├── 2023/
   │   ├── AGM_2023_SACKOBA_Qatar/
   │   │   ├── image1.jpg
   │   │   ├── image2.jpg
   │   │   └── ...
   │   └── Other_Event_2023/
   └── 2022/
       └── Event_2022/
   ```

2. **Image Requirements**:
   - Supported formats: JPG, JPEG, PNG, GIF, WebP
   - Recommended size: 800x600 pixels or larger
   - File naming: Use descriptive names with underscores

### Step 2: Update Image Data

1. **Edit `src/data/eventImageData.ts`**:
   ```typescript
   {
     url: '/images/event_images/2023/AGM_2023_SACKOBA_Qatar/your_image.jpg',
     title: 'Descriptive Title',
     filename: 'your_image.jpg'
   }
   ```

2. **Replace Placeholder URLs**: Currently using Unsplash placeholder images. Replace with actual image paths.

### Step 3: Add New Events

To add a new event category:

```typescript
{
  id: 'unique_event_id',
  name: 'Event Display Name',
  year: '2023',
  folderPath: 'src/assets/images/event_images/2023/Event_Folder',
  images: [
    {
      url: '/images/event_images/2023/Event_Folder/image1.jpg',
      title: 'Image Description',
      filename: 'image1.jpg'
    }
    // Add more images...
  ]
}
```

## Current Event Categories

### 2023 Events
- AGM 2023 SACKOBA Qatar (8 images)
- Sinhala & Tamil New Year and Iftar Celebration 2023 (3 images)

### 2022 Events
- College Day and Feast of St Anthony of Padua 2022 (6 images)
- Aluth Avurudu Iftar Celebration 2022 (3 images)

## Available Event Folders

The following event folders are available in `src/assets/images/event_images/`:

### 2023
- AGM_2023_SACKOBA_Qatar
- SACKOBAQ_College_Day_Celebrations
- Sinhala_&_Tamil_New_Year_and_Iftar_Celebration_2023_SACKOBA_Qatar

### 2022
- Aluth_Avurudu_Iftar_Celebration_2022
- College_Day_and_Feast_of_St_Anthony_of_Padua_2022

### 2020
- Family_Activity_Day_2020
- Old_Anandians_Bowling_Tournament

### 2018
- Rayyan_Talons_Smash_2018

### 2017
- Awurudu_Udawa_2017
- College_Day_-_AGM___Annual_Iftar_-2017
- Dark_horse_Cricket_Tournament_-_2017_Champions
- Day_Out_on_a_Boat_2017
- Feast_of_St.Anthony_2017
- Lions_Srilankan_Schools_Touch_Rugby_7s_-_2017
- Silent_Night_2017

### 2016
- Antonian_Festive_Celebration_-_2016
- Green_Touch_Rugby_7s_and_Champions_Dinner_-_2016
- Lions_Srilankan_Cricket_Tournament_-_2016
- SACKOBA_Q_Big_Day_Out_2016

### 2015
- Feast_of_St.Anthony_of_Padua_2015
- IFTAR_2015
- Lions_Cricket_7s_2015
- Lions_Rugby_7s_2015
- Special_General_Meeting_2015

### 2013
- Trinity_Beach_Carnival_2013

### 2012
- Boat_2012
- COLLEGE_DAY_CELEBRATION_2012
- Fuwairit_Annual_Beach_Trip_2012
- LIONS_RUGBY_7S_2012
- OZEE_7S_2012
- SPECIAL_MEETINGS

## Next Steps

1. **Copy Images**: Move images from `src/assets/images/event_images/` to `public/images/event_images/`
2. **Update Data**: Replace placeholder URLs in `eventImageData.ts` with actual image paths
3. **Add Missing Events**: Add data for all available event folders
4. **Optimize Images**: Consider image optimization for better performance
5. **Add Metadata**: Include more descriptive titles and captions

## Technical Notes

- Images are served from the `public` folder in Vite
- The gallery uses lazy loading for better performance
- Modal supports keyboard navigation (ESC to close)
- Download functionality is available for all images
- Responsive design works on all screen sizes

## Troubleshooting

- **Images not loading**: Check that images are in the `public/images/` directory
- **Wrong paths**: Ensure URLs start with `/images/` not `/src/assets/`
- **Performance issues**: Consider image optimization and lazy loading
- **Missing images**: Add fallback images or error handling
