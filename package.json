{"name": "sackobaqatar.com", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.47.10", "@types/react-lazy-load-image-component": "^1.6.4", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.13.1", "lucide-react": "^0.468.0", "react": "^18.3.1", "react-country-flag": "^3.1.0", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-icons": "^5.4.0", "react-lazy-load-image-component": "^1.6.3", "react-masonry-css": "^1.0.16", "react-router-dom": "^7.0.2", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.15.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.10.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.1.6"}}